/// 회원탈퇴 전용 로딩 다이얼로그
/// 
/// 회원탈퇴 과정의 각 단계를 명확하게 표시하여 사용자 경험을 개선합니다.

import 'package:flutter/material.dart';

/// 회원탈퇴 진행 단계
enum AccountDeletionStep {
  authenticating('재인증 중...', '비밀번호를 확인하고 있습니다.'),
  deletingServerData('서버 데이터 삭제 중...', 'Firebase에서 모든 데이터를 삭제하고 있습니다.'),
  deletingLocalData('로컬 데이터 정리 중...', '기기에 저장된 데이터를 정리하고 있습니다.'),
  restarting('앱 재시작 중...', '완전한 초기화를 위해 앱을 재시작합니다.');

  const AccountDeletionStep(this.title, this.description);
  
  final String title;
  final String description;
}

/// 회원탈퇴 진행 상황을 표시하는 다이얼로그
class AccountDeletionDialog extends StatefulWidget {
  const AccountDeletionDialog({super.key});

  @override
  State<AccountDeletionDialog> createState() => _AccountDeletionDialogState();
}

class _AccountDeletionDialogState extends State<AccountDeletionDialog>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _fadeController;
  late Animation<double> _progressAnimation;
  late Animation<double> _fadeAnimation;

  AccountDeletionStep _currentStep = AccountDeletionStep.authenticating;
  double _progress = 0.0;

  @override
  void initState() {
    super.initState();
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  @override
  void dispose() {
    _progressController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  /// 현재 단계 업데이트
  void updateStep(AccountDeletionStep step) {
    if (mounted) {
      setState(() {
        _currentStep = step;
        _progress = (step.index + 1) / AccountDeletionStep.values.length;
      });
      
      _progressController.animateTo(_progress);
    }
  }

  /// 오류 상태로 변경
  void showError(String errorMessage) {
    if (mounted) {
      setState(() {
        _currentStep = AccountDeletionStep.authenticating; // 기본값으로 리셋
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // 뒤로가기 방지
      child: Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 아이콘
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.delete_forever,
                    color: Colors.red,
                    size: 32,
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // 제목
                Text(
                  '회원탈퇴 진행 중',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 16),
                
                // 현재 단계
                Text(
                  _currentStep.title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 8),
                
                // 단계 설명
                Text(
                  _currentStep.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 24),
                
                // 진행률 표시
                Column(
                  children: [
                    AnimatedBuilder(
                      animation: _progressAnimation,
                      builder: (context, child) {
                        return LinearProgressIndicator(
                          value: _progressAnimation.value,
                          backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).colorScheme.primary,
                          ),
                        );
                      },
                    ),
                    
                    const SizedBox(height: 8),
                    
                    Text(
                      '${(_progress * 100).toInt()}% 완료',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                // 로딩 인디케이터
                const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                
                const SizedBox(height: 16),
                
                // 안내 메시지
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 16,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '잠시만 기다려주세요. 이 과정은 몇 분 정도 소요될 수 있습니다.',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 회원탈퇴 다이얼로그 관리자
class AccountDeletionDialogManager {
  static AccountDeletionDialog? _currentDialog;
  static GlobalKey<_AccountDeletionDialogState>? _dialogKey;

  /// 다이얼로그 표시
  static Future<void> show(BuildContext context) async {
    if (_currentDialog != null) return; // 이미 표시 중이면 무시

    _dialogKey = GlobalKey<_AccountDeletionDialogState>();
    _currentDialog = AccountDeletionDialog(key: _dialogKey);

    showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => _currentDialog!,
    );
  }

  /// 단계 업데이트
  static void updateStep(AccountDeletionStep step) {
    _dialogKey?.currentState?.updateStep(step);
  }

  /// 오류 표시
  static void showError(String errorMessage) {
    _dialogKey?.currentState?.showError(errorMessage);
  }

  /// 다이얼로그 닫기
  static void dismiss(BuildContext context) {
    if (_currentDialog != null) {
      Navigator.of(context, rootNavigator: true).pop();
      _currentDialog = null;
      _dialogKey = null;
    }
  }

  /// 현재 다이얼로그가 표시 중인지 확인
  static bool get isShowing => _currentDialog != null;
}
